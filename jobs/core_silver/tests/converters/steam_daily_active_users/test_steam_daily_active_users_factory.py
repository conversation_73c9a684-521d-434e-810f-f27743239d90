from datetime import date

from core_silver.observation_converter.converters.steam_daily_active_users import (
    SteamDailyActiveUsersConverter,
)


def test_generate_empty_steam_daily_active_users(steam_raw_daily_active_users_factory):
    steam_raw_dau = steam_raw_daily_active_users_factory(
        start_date=date(2023, 10, 1), end_date=date(2023, 10, 1), rows=[]
    )
    assert steam_raw_dau.start_date == date(2023, 10, 1)
    assert steam_raw_dau.end_date == date(2023, 10, 1)
    assert steam_raw_dau.skus == []


def test_convert_steam_daily_active_users_empty(
    generate_raw_steam_daily_active_users_report,
):
    raw_report = generate_raw_steam_daily_active_users_report(rows=0)
    converter = SteamDailyActiveUsersConverter(raw_report)
    result = converter.convert()
    assert result.df.is_empty()
