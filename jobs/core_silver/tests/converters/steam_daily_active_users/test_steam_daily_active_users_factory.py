from datetime import date


def test_generate_empty_steam_daily_active_users(steam_raw_daily_active_users_factory):
    steam_raw_dau = steam_raw_daily_active_users_factory(
        start_date=date(2023, 10, 1), end_date=date(2023, 10, 1), rows=[]
    )
    assert steam_raw_dau.start_date == date(2023, 10, 1)
    assert steam_raw_dau.end_date == date(2023, 10, 1)
    assert steam_raw_dau.manifest_json == {
        "manifest.json": {
            "dateFrom": "2023-10-01",
            "dateTo": "2023-10-01",
            "fileMetaData": {
                "daily_2023-10-01_2023-10-01.csv": {
                    "dateFrom": "2023-10-01",
                    "dateTo": "2023-10-01",
                },
                "monthly_2023-10-01_2023-10-01.csv": {
                    "dateFrom": "2023-10-01",
                    "dateTo": "2023-10-01",
                },
            },
        },
    }
    assert steam_raw_dau.daily_active_users_csv == {
        "daily_2023-10-01_2023-10-01.csv": "account,portal,date,product,product_id,count\n",
        "monthly_2023-10-01_2023-10-01.csv": "account,portal,year,month,product,product_id,count\n",
    }


def test_generate_one_day_of_steam_daily_active_users(
    steam_raw_daily_active_users_factory,
):
    result = steam_raw_daily_active_users_factory(
        start_date=date(2023, 10, 1),
        end_date=date(2023, 10, 1),
    )

    assert result.start_date == date(2023, 10, 1)
    assert result.end_date == date(2023, 10, 1)
    assert result.manifest_json == {
        "manifest.json": {
            "dateFrom": "2023-10-01",
            "dateTo": "2023-10-01",
            "fileMetaData": {
                "daily_2023-10-01_2023-10-01.csv": {
                    "dateFrom": "2023-10-01",
                    "dateTo": "2023-10-01",
                },
                "monthly_2023-10-01_2023-10-01.csv": {
                    "dateFrom": "2023-10-01",
                    "dateTo": "2023-10-01",
                },
            },
        },
    }
    assert result.daily_active_users_csv == {
        "daily_2023-10-01_2023-10-01.csv": (
            "account,portal,date,product,product_id,count\n"
            "Test account,steam,2023-10-01,Test product,42,1\n"
        ),
        "monthly_2023-10-01_2023-10-01.csv": "account,portal,year,month,product,product_id,count\n",
    }


def test_generate_two_days_of_steam_daily_active_users(
    steam_raw_daily_active_users_factory,
):
    result = steam_raw_daily_active_users_factory(
        start_date=date(2024, 4, 1),
        end_date=date(2024, 4, 2),
    )

    assert result.start_date == date(2024, 4, 1)
    assert result.end_date == date(2024, 4, 2)
    assert result.daily_active_users_csv == {
        "daily_2024-04-01_2024-04-02.csv": (
            "account,portal,date,product,product_id,count\n"
            "Test account,steam,2024-04-01,Test product,42,1\n"
            "Test account,steam,2024-04-02,Test product,42,1\n"
        ),
        "monthly_2024-04-01_2024-04-02.csv": "account,portal,year,month,product,product_id,count\n",
    }


def test_generate_steam_daily_active_users_with_custom_data(
    steam_raw_daily_active_users_factory,
):
    result = steam_raw_daily_active_users_factory(
        start_date=date(2023, 10, 1),
        end_date=date(2023, 10, 1),
        rows__product="SUPERHOT VR",
        rows__product_id="617830",
        rows__count=1006,
    )

    assert result.start_date == date(2023, 10, 1)
    assert result.end_date == date(2023, 10, 1)
    assert result.daily_active_users_csv == {
        "daily_2023-10-01_2023-10-01.csv": (
            "account,portal,date,product,product_id,count\n"
            "Test account,steam,2023-10-01,SUPERHOT VR,617830,1006\n"
        ),
        "monthly_2023-10-01_2023-10-01.csv": "account,portal,year,month,product,product_id,count\n",
    }


def test_generate_steam_daily_active_users_multiple_products(
    steam_raw_daily_active_users_factory,
):
    from tests.converters.steam_daily_active_users.conftest import (
        SteamDailyActiveUsersRowFactory,
    )

    custom_rows = [
        SteamDailyActiveUsersRowFactory(
            date=date(2023, 10, 1),
            product="SUPERHOT VR",
            product_id="617830",
            count=1006,
        ),
        SteamDailyActiveUsersRowFactory(
            date=date(2023, 10, 1),
            product="SUPERHOT",
            product_id="322500",
            count=2500,
        ),
    ]

    result = steam_raw_daily_active_users_factory(
        start_date=date(2023, 10, 1),
        end_date=date(2023, 10, 1),
        rows=custom_rows,
    )

    assert result.start_date == date(2023, 10, 1)
    assert result.end_date == date(2023, 10, 1)
    assert result.daily_active_users_csv == {
        "daily_2023-10-01_2023-10-01.csv": (
            "account,portal,date,product,product_id,count\n"
            "Test account,steam,2023-10-01,SUPERHOT VR,617830,1006\n"
            "Test account,steam,2023-10-01,SUPERHOT,322500,2500\n"
        ),
        "monthly_2023-10-01_2023-10-01.csv": "account,portal,year,month,product,product_id,count\n",
    }
