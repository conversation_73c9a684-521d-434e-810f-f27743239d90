from zipfile import BadZipFile

import pandas as pd
import pandera as pa
from pandera import Column

from core_silver.observation_converter.common_columns import (
    generate_portal_platform_region,
    generate_unique_sku_id,
)
from core_silver.observation_converter.converters.base_converter import (
    BaseDailyActiveUsersConverter,
    extract_csvs_from_raw_file,
    get_manifest_data_from_raw_file,
)
from core_silver.observation_converter.converters.exceptions import (
    FileExtractionError,
    FileSchemaError,
    InvalidManifest,
)
from core_silver.observation_converter.pandas_utils import InvalidSchema, enforce_schema
from data_sdk.domain import DisplayPortal, Portal
from data_sdk.domain.countries import get_country_alpha_3
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.regions import Region
from data_sdk.domain.stores import Store


class SteamDailyActiveUsersConverter(BaseDailyActiveUsersConverter):
    _schema = pa.DataFrameSchema({
        "account": Column(pa.String, coerce=True),
        "portal": Column(pa.String, coerce=True),
        "date": Column(pa.DateTime, coerce=True),
        "product": Column(pa.String, coerce=True),
        "product_id": Column(pa.String, coerce=True),
        "count": Column(pa.Int, coerce=True),
    })

    def parse(self, raw_file: bytes) -> pd.DataFrame:
        df = self._extract_files(raw_file)
        df.sort_index(ascending=True, inplace=True)
        try:
            return enforce_schema(df, self._schema)
        except InvalidSchema as ex:
            raise FileSchemaError(str(ex))

    def _convert(self) -> pd.DataFrame:
        manifest = self._raw_report.metadata
        parsed_df = self.parse(self._raw_report.raw_file)

        if parsed_df.empty:
            return pd.DataFrame()

        converted_df = pd.DataFrame()

        return converted_df

    def _extract_files(
        self, raw_file: bytes, file_list: list[str] | None = None
    ) -> pd.DataFrame:
        try:
            self.manifest_from_file = get_manifest_data_from_raw_file(raw_file)
            raw_df = extract_csvs_from_raw_file(
                raw_file,
                df_transform=self._human_name_from_manifest,
                file_list=file_list if file_list else None,
                sep=",",
                header=0,
                parse_dates=True,
                quotechar='"',
                keep_default_na=False,
                na_values=[""],
                dtype={
                    "account": str,
                    "portal": str,
                    "date": str,
                    "product": str,
                    "product_id": str,
                    "count": str,
                },
            )
        except (ValueError, BadZipFile) as ex:
            raise FileExtractionError(str(ex))
        return raw_df

    def _human_name_from_manifest(
        self, data_frame: pd.DataFrame, file_name: str
    ) -> pd.DataFrame:
        try:
            data_frame["Human Name"] = self.manifest_from_file["metadata"][file_name][
                "productName"
            ]
        except KeyError:
            raise InvalidManifest(message="ProductName not found in manifest")

        return data_frame
