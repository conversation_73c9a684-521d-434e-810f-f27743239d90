from enum import Enum
from typing import Annotated

import pandera as pa
import pandera.polars as pap
from pandera.engines.polars_engine import Categorical, Date, DateTime
from pandera.typing import Series

from data_sdk.domain import DisplayPortal
from data_sdk.domain.countries import countries_dictionary
from data_sdk.domain.platform import DisplayPlatform
from data_sdk.domain.regions import Region
from data_sdk.domain.stores import Store
from data_sdk.utils.pandera import Enum as PanderaEnum


class ObservationType(str, Enum):
    SALES = "sales"
    WISHLIST_ACTIONS = "wishlist_actions"
    WISHLIST_COHORTS = "wishlist_cohorts"
    WISHLIST_BALANCE = "wishlist_balance"
    CUMULATIVE_WISHLIST_SALES = "cumulative_wishlist_sales"
    VISIBILITY = "visibility"
    DISCOUNTS = "discounts"


ALL_OBSERVATION_TYPES = [obs_type for obs_type in ObservationType]


def HashString():
    return pa.Field(
        str_length={
            "min_value": 32,
            "max_value": 32,
        }  # TODO: Use StringLength.TINY when it will be moved to data_sdk)
    )


PortalColumn = Series[Annotated[PanderaEnum, list(DisplayPortal)]]
PlatformColumn = Series[Annotated[PanderaEnum, list(DisplayPlatform)]]
RegionColumn = Series[Annotated[PanderaEnum, list(Region)]]
StoreColumn = Series[Annotated[PanderaEnum, list(Store)]]
CountryCodeColumn = Series[
    Annotated[PanderaEnum, sorted(list(set(countries_dictionary.values())))]
]

CategoricalColumn = Series[Annotated[Categorical, "physical"]]


class SalesModel(pap.DataFrameModel):
    sku_id: str = pa.Field()
    portal: PortalColumn
    platform: PlatformColumn
    region: RegionColumn
    transaction_type: CategoricalColumn
    payment_instrument: CategoricalColumn
    tax_type: CategoricalColumn
    sale_modificator: CategoricalColumn
    acquisition_platform: CategoricalColumn
    acquisition_origin: CategoricalColumn
    iap_flag: str = pa.Field()
    date: Date = pa.Field()
    retailer_tag: CategoricalColumn
    human_name: CategoricalColumn
    store_id: CategoricalColumn
    base_price_local: float = pa.Field(nullable=True)
    bundle_name: CategoricalColumn
    net_sales: float = pa.Field()
    gross_returned: float = pa.Field()
    gross_sales: float = pa.Field()
    units_returned: int = pa.Field()
    units_sold: int = pa.Field()
    free_units: int = pa.Field()
    price_local: float = pa.Field()
    price_usd: float = (
        pa.Field()
    )  # TODO: We have negative values and it doesn't look good
    store: StoreColumn
    abbreviated_name: CategoricalColumn
    net_sales_approx: float = pa.Field()
    category: CategoricalColumn
    # natural keys
    studio_id: int = pa.Field()
    report_id: int = pa.Field()
    unique_sku_id: CategoricalColumn
    country_code: CountryCodeColumn
    currency_code: CategoricalColumn
    # artificial keys
    hash_acquisition_properties: CategoricalColumn
    portal_platform_region: CategoricalColumn

    class Config:
        strict = True
        coerce = True


class VisibilityModel(pap.DataFrameModel):
    page_category: CategoricalColumn
    page_feature: CategoricalColumn
    impressions: int = pa.Field()
    visits: int = pa.Field()
    owner_impressions: int = pa.Field()
    owner_visits: int = pa.Field()
    sku_id: str = pa.Field()
    date: Date = pa.Field()
    human_name: CategoricalColumn
    platform: PlatformColumn
    portal: PortalColumn
    store_id: CategoricalColumn
    region: RegionColumn
    store: StoreColumn
    abbreviated_name: CategoricalColumn
    navigation: str = pa.Field()
    # natural keys
    unique_sku_id: CategoricalColumn
    report_id: int = pa.Field()
    studio_id: int = pa.Field()
    # artificial keys
    portal_platform_region: CategoricalColumn
    hash_traffic_source: CategoricalColumn

    class Config:
        strict = True
        coerce = True


class WishlistCohortsModel(pap.DataFrameModel):
    platform: PlatformColumn
    region: RegionColumn
    portal: PortalColumn
    date: Date = pa.Field()
    human_name: CategoricalColumn
    month_cohort: str = pa.Field()
    total_conversions: int = pa.Field()
    purchases_and_activations: int = pa.Field()
    gifts: int = pa.Field()
    sku_id: str = pa.Field()
    store_id: CategoricalColumn
    store: StoreColumn
    abbreviated_name: CategoricalColumn
    # natural keys
    report_id: int = pa.Field()
    unique_sku_id: CategoricalColumn
    studio_id: int = pa.Field()
    # artificial keys
    portal_platform_region: CategoricalColumn

    class Config:
        strict = True
        coerce = True


class CumulativeWishlistSalesModel(pap.DataFrameModel):
    platform: PlatformColumn
    region: RegionColumn
    portal: PortalColumn
    date: Date = pa.Field()
    cumulative_wishlist_sales: int = pa.Field()
    source_based_conversion_rate: str = pa.Field()
    source_based_total_downloads: int = pa.Field()
    human_name: CategoricalColumn
    sku_id: str = pa.Field()
    store_id: CategoricalColumn
    store: StoreColumn
    abbreviated_name: CategoricalColumn
    # natural keys
    report_id: int = pa.Field()
    studio_id: int = pa.Field()
    unique_sku_id: CategoricalColumn
    # artificial keys
    portal_platform_region: CategoricalColumn

    class Config:
        strict = True
        coerce = True


class WishlistActionsModel(pap.DataFrameModel):
    platform: PlatformColumn
    region: RegionColumn
    portal: PortalColumn
    date: Date = pa.Field()
    human_name: CategoricalColumn
    adds: int = pa.Field()
    deletes: int = pa.Field()
    purchases_and_activations: int = pa.Field()
    gifts: int = pa.Field()
    sku_id: str = pa.Field()
    store_id: CategoricalColumn
    store: StoreColumn
    abbreviated_name: CategoricalColumn
    # natural keys
    report_id: int = pa.Field()
    studio_id: int = pa.Field()
    unique_sku_id: CategoricalColumn
    country_code: CountryCodeColumn
    # artificial keys
    portal_platform_region: CategoricalColumn

    class Config:
        strict = True
        coerce = True


class WishlistBalanceModel(pap.DataFrameModel):
    platform: PlatformColumn
    region: RegionColumn
    portal: PortalColumn
    date: Date = pa.Field()
    human_name: CategoricalColumn
    balance: int = pa.Field()
    sku_id: str = pa.Field()
    store_id: CategoricalColumn
    store: StoreColumn
    abbreviated_name: CategoricalColumn
    # natural keys
    report_id: int = pa.Field()
    studio_id: int = pa.Field()
    unique_sku_id: CategoricalColumn
    country_code: CountryCodeColumn
    # artificial keys
    portal_platform_region: CategoricalColumn

    class Config:
        strict = True
        coerce = True


class DailyActiveUsersModel(pap.DataFrameModel):
    platform: PlatformColumn
    region: RegionColumn
    portal: PortalColumn
    date: Date = pa.Field()
    human_name: CategoricalColumn
    daily_active_users: int = pa.Field()
    sku_id: str = pa.Field()
    store_id: CategoricalColumn
    store: StoreColumn
    abbreviated_name: CategoricalColumn
    # natural keys
    report_id: int = pa.Field()
    studio_id: int = pa.Field()
    unique_sku_id: CategoricalColumn
    country_code: CountryCodeColumn
    # artificial keys
    portal_platform_region: CategoricalColumn

    class Config:
        strict = True
        coerce = True


class DiscountType(str, Enum):
    CUSTOM = "custom"
    STORE = "store"


class DiscountsModel(pap.DataFrameModel):
    create_time: DateTime = pa.Field(dtype_kwargs={"time_zone": "UTC"})
    update_time: DateTime = pa.Field(dtype_kwargs={"time_zone": "UTC"})
    platform: PlatformColumn
    region: RegionColumn
    portal: PortalColumn
    event_name: str = pa.Field()
    datetime_from: DateTime = pa.Field(dtype_kwargs={"time_zone": "UTC"})
    datetime_to: DateTime = pa.Field(dtype_kwargs={"time_zone": "UTC"})
    is_event_joined: bool = pa.Field()
    discount_depth: int = pa.Field()
    unique_event_id: str = pa.Field()
    base_event_id: str = pa.Field()
    group_id: str = pa.Field()
    base_sku_id: str = pa.Field()
    source_specific_discount_sku_id: str = pa.Field()
    triggers_cooldown: bool = pa.Field()
    major: bool = pa.Field()
    discount_type: Series[Categorical] = pa.Field(isin=list(DiscountType))
    max_discount_percentage: int = pa.Field()
    price_increase_time: DateTime = pa.Field(dtype_kwargs={"time_zone": "UTC"})
    promo_length: int = pa.Field()
    sales_unique_sku_id: str = pa.Field()
    # natural keys
    studio_id: int = pa.Field()
    # Probably will evolve in the future, but keeping it for backwards compatibility
    unique_sku_id: CategoricalColumn
    report_id: int = pa.Field()
    # artificial keys
    portal_platform_region: CategoricalColumn

    class Config:
        strict = True
        coerce = True
